"use client";

import { useEffect, useLayoutEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { But<PERSON> } from "@/app/components/ui/button";
import { MoreVertical, Copy, Trash2, Edit, Save, X } from "lucide-react";
import { FileRecord } from "@/app/lib/types";
import DeleteConfirmationModal from "@/app/components/DeleteConfirmationModal";

export interface FileActionsDropdownProps {
  file: FileRecord;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCopyLink: (file: FileRecord) => void;
  onDelete: (file: FileRecord) => void;
  onEditLimit: () => void;
  onSaveLimit: (fileId: string) => void;
  onCancelLimit: () => void;
  isEditing: boolean;
  editingValue: string;
  setEditingValue: (value: string) => void;
  savingLimit: boolean;
}

/**
 * Themed actions dropdown for a file row, rendered in a portal (body)
 * so it overlays parent containers and avoids inner scrolling/clipping.
 */
export default function FileActionsDropdown({
  file,
  open,
  onOpenChange,
  onCopyLink,
  onDelete,
  onEditLimit,
  onSaveLimit,
  onCancelLimit,
  isEditing,
  editingValue,
  setEditingValue,
  savingLimit,
}: FileActionsDropdownProps) {
  const btnRef = useRef<HTMLButtonElement | null>(null);
  const menuRef = useRef<HTMLDivElement | null>(null);

  // Inline confirm UI removed; we now use modals
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Close on outside click or Escape
  useEffect(() => {
    // Use pointerdown to run before onClick, ensuring correct toggle behavior
    const handlePointerDown = (event: MouseEvent) => {
      const target = event.target as Node;

      // If clicking trigger button:
      if (btnRef.current && btnRef.current.contains(target)) {
        // Toggle immediately based on current state
        onOpenChange(!open);
        // Prevent the subsequent onClick/open handlers from re-opening
        event.preventDefault();
        event.stopPropagation();
        return;
      }

      if (!open) return;

      // Clicks inside the menu should not close
      if (menuRef.current && menuRef.current.contains(target)) return;

      // Otherwise close
      onOpenChange(false);
    };

    const handleKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        // If modal is open, let the modal's own handler close it.
        if (showDeleteModal) return;
        onOpenChange(false);
      }
    };

    // Use pointerdown capturing to avoid focus/blur races and let menu buttons work
    document.addEventListener("pointerdown", handlePointerDown as any, true);
    document.addEventListener("keydown", handleKey);
    return () => {
      document.removeEventListener("pointerdown", handlePointerDown as any, true);
      document.removeEventListener("keydown", handleKey);
    };
  }, [open, onOpenChange, showDeleteModal]);

  // Position the floating menu anchored to the trigger button
  useLayoutEffect(() => {
    if (!open || !btnRef.current || !menuRef.current) return;

    const button = btnRef.current;
    const menu = menuRef.current;

    // Reset styles to calculate natural dimensions, but keep it hidden initially
    menu.style.position = "fixed";
    menu.style.top = "-9999px";
    menu.style.left = "-9999px";
    menu.style.zIndex = "1000";
    menu.style.visibility = "hidden";

    // Force a reflow to get accurate dimensions
    const menuRect = menu.getBoundingClientRect();
    const buttonRect = button.getBoundingClientRect();

    // Calculate position
    let top, left;

    // Position below button by default
    top = buttonRect.bottom + 4;
    left = buttonRect.left;

    // Adjust if menu would go off right edge
    if (left + menuRect.width > window.innerWidth) {
      left = window.innerWidth - menuRect.width - 8;
    }

    // Adjust if menu would go off left edge
    if (left < 8) {
      left = 8;
    }

    // Flip to above if not enough space below and enough space above
    if (buttonRect.bottom + menuRect.height + 4 > window.innerHeight &&
        buttonRect.top - menuRect.height - 4 > 0) {
      top = buttonRect.top - menuRect.height - 4;
    }

    // Ensure menu doesn't go off bottom
    if (top + menuRect.height > window.innerHeight) {
      top = window.innerHeight - menuRect.height - 8;
    }

    // Ensure menu doesn't go off top
    if (top < 8) {
      top = 8;
    }

    // Now set the final position and make it visible
    menu.style.top = `${top}px`;
    menu.style.left = `${left}px`;
    menu.style.visibility = "visible";
  }, [open]);


  const menu = open ? (
    <div ref={menuRef} role="menu" aria-label="Filhandlinger" className="w-56">
      {/* panel */}
      <div className="rounded-xl overflow-hidden border border-gray-200 bg-white shadow-lg py-1">
        <div className="py-1">

          {/* Copy link */}
          <button
            type="button"
            role="menuitem"
            onMouseDown={(e) => e.stopPropagation()}
            onClick={(e) => {
              e.stopPropagation();
              onCopyLink(file);
              onOpenChange(false);
            }}
            className="group w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors"
          >
            <span className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-blue-100 text-blue-600 group-hover:bg-blue-200">
              <Copy className="h-4 w-4" />
            </span>
            <div className="flex-1 text-left">Kopier download link</div>
          </button>

          {/* Edit download limit */}
          <button
            type="button"
            role="menuitem"
            onMouseDown={(e) => e.stopPropagation()}
            onClick={(e) => {
              e.stopPropagation();
              onEditLimit();
              onOpenChange(false);
            }}
            className="group w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-gray-700 hover:text-blue-600 hover:bg-blue-50 transition-colors"
          >
            <span className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-blue-100 text-blue-600 group-hover:bg-blue-200">
              <Edit className="h-4 w-4" />
            </span>
            <div className="flex-1 text-left">Rediger download grænse</div>
          </button>

          {/* Delete - opens modal (and close dropdown) */}
          <button
            type="button"
            role="menuitem"
            onMouseDown={(e) => e.stopPropagation()}
            onClick={(e) => {
              e.stopPropagation();
              // Close dropdown first, then open modal next tick for smooth UX
              onOpenChange(false);
              setTimeout(() => setShowDeleteModal(true), 0);
            }}
            className="group w-full flex items-center gap-3 px-3 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 transition-colors"
          >
            <span className="inline-flex h-8 w-8 items-center justify-center rounded-md bg-red-100 text-red-600 group-hover:bg-red-200">
              <Trash2 className="h-4 w-4" />
            </span>
            <div className="flex-1 text-left">Slet fil</div>
          </button>
        </div>
      </div>
    </div>
  ) : null;

  return (
    <div className="relative">
      <Button
        ref={btnRef}
        aria-haspopup="menu"
        aria-expanded={open}
        aria-label="Åbn handlinger"
        variant="ghost"
        size="sm"
        // No-op: toggle is handled in capturing mousedown to avoid race conditions with portals
        onClick={(e) => {
          // onClick remains a no-op to avoid double toggling with pointerdown
          e.preventDefault();
          e.stopPropagation();
        }}
        className="hover:bg-blue-50 text-gray-900 hover:text-blue-700 dark:hover:bg-blue-50/70 rounded-lg"
      >
        <MoreVertical className="h-4 w-4" />
      </Button>

      {/* Render menu into body to escape clipping/scroll of table/card */}
      {open && typeof window !== "undefined"
        ? createPortal(menu, document.body)
        : null}

      {/* Delete confirmation modal */}
      <DeleteConfirmationModal
        open={showDeleteModal}
        title="Er du sikker?"
        description="Denne handling kan ikke fortrydes. Vil du slette filen?"
        confirmText="Slet"
        cancelText="Annuller"
        onCancel={() => setShowDeleteModal(false)}
        onConfirm={() => {
          onDelete(file);
          setShowDeleteModal(false);
        }}
      />

    </div>
  );
}