"use client"

import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Progress } from "@/app/components/ui/progress"
import { Upload, File, Download, Calendar, Settings, TrendingUp, X, Copy, Trash2, Edit, MoreVertical, Save, Check } from "lucide-react"
import { formatFileSize, formatDate, calculateUsagePercentage, getPlanDisplayName, filterExpiredFiles } from "@/app/lib/utils"
import { PLAN_CONFIGS } from "@/app/lib/plans"
import { FileRecord, UsageStats } from "@/app/lib/types"
import RequestMoreUsageModal from "@/app/components/RequestMoreUsageModal"
import FileActionsDropdown from "@/app/components/FileActionsDropdown"
import { toast } from "sonner"

export default function DashboardPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [files, setFiles] = useState<FileRecord[]>([])
  const [usage, setUsage] = useState<UsageStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isDragOver, setIsDragOver] = useState(false)
  const [openDropdown, setOpenDropdown] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [currentUploadIndex, setCurrentUploadIndex] = useState(0)
  const [totalFiles, setTotalFiles] = useState(0)
  const [uploadStatus, setUploadStatus] = useState('')
  
  // Download limit editing state
  const [editingLimit, setEditingLimit] = useState<string | null>(null)
  const [editingValue, setEditingValue] = useState<string>('')
  const [savingLimit, setSavingLimit] = useState<string | null>(null)

  // One-shot guard to prevent multiple reloads
  const hasTriggeredHardReload = useRef(false)

  // Modal: Request more usage
  const [isRequestOpen, setIsRequestOpen] = useState(false)
  const [requestAmount, setRequestAmount] = useState<string>("")
  const [requestMessage, setRequestMessage] = useState<string>("")
  const [requestSubmitting, setRequestSubmitting] = useState(false)

  const resetRequestForm = () => {
    setRequestAmount("")
    setRequestMessage("")
  }

  const submitRequest = async () => {
    try {
      setRequestSubmitting(true)
      // Placeholder submit - no backend endpoint yet.
      // You can connect this to an API route later.
      
      toast.success("Din forespørgsel er sendt. Vi vender tilbage hurtigst muligt.")
      setIsRequestOpen(false)
      resetRequestForm()
    } catch (e) {
      toast.error("Kunne ikke sende forespørgslen.")
    } finally {
      setRequestSubmitting(false)
    }
  }

  useEffect(() => {
    if (status === "loading") return
    loadDashboardData()
  }, [status])

  const loadDashboardData = async () => {
    try {
      // Load usage stats
      const usageResponse = await fetch('/api/user/usage')
      if (usageResponse.ok) {
        const usageData = await usageResponse.json()
        setUsage(usageData.data)
      }

      // Load files for both guests and authenticated users
      const filesResponse = await fetch('/api/files')
      if (filesResponse.ok) {
        const filesData = await filesResponse.json()
        // Double-check: Filter out any expired files that might have slipped through the API
        // This ensures expired files are never displayed in the UI, even if there's a timing issue
        const nonExpiredFiles = filterExpiredFiles(filesData.data || []) as FileRecord[]
        setFiles(nonExpiredFiles)
      }
    } catch (error) {
    } finally {
      setLoading(false)
    }
  }


  const handleCopyLink = (file: FileRecord) => {
    const url = `${location.origin}/d/${file._id}`
    navigator.clipboard.writeText(url).then(
      () => toast.success("Link kopieret"),
      () => toast.error("Kunne ikke kopiere link")
    )
  }

  const handleDelete = async (file: FileRecord) => {
    try {
      // call existing API delete if present; else optimistic remove and toast
      const res = await fetch(`/api/files/${file._id}`, { method: "DELETE" }).catch(() => null)
      if (!res || !res.ok) {
        // fall back to optimistic if API not available
      }
      setFiles(prev => prev.filter(f => f._id !== file._id))
      toast.success("Fil slettet")
    } catch {
      toast.error("Kunne ikke slette filen")
    }
  }

  // Helper to detect if DataTransfer contains any directory entries
  const hasDirectoryInDataTransfer = (dt: DataTransfer): boolean => {
    // Prefer Chromium-specific webkitGetAsEntry if available
    const items = (dt.items ? Array.from(dt.items) : []) as DataTransferItem[]
    for (const item of items) {
      if (item.kind === "file" && typeof (item as any).webkitGetAsEntry === "function") {
        const entry = (item as any).webkitGetAsEntry()
        if (entry && entry.isDirectory) {
          return true
        }
      }
    }
    // Fallback heuristic: some browsers put a single File with size 0 and empty type for folders,
    // but this is not reliable; we rely primarily on webkitGetAsEntry.
    return false
  }

  const [folderModalOpen, setFolderModalOpen] = useState(false)

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)

    // Block directories; show modal instead of accepting drop
    if (hasDirectoryInDataTransfer(e.dataTransfer)) {
      setFolderModalOpen(true)
      return
    }

    const files = Array.from(e.dataTransfer.files)
    handleFileSelection(files)
  }, [])

  const handleFileSelection = (files: File[]) => {
    // Normalize user plan safely to avoid undefined planConfig
    const rawPlan = (((session?.user as any)?.plan as string) || 'free').toLowerCase()
    const PLAN_ALIASES: Record<string, keyof typeof PLAN_CONFIGS> = {
      guest: 'guest',
      free: 'free',
      gratis: 'free',
      basis: 'upgrade1',
      basic: 'upgrade1',
      upgrade1: 'upgrade1',
      pro: 'upgrade2',
      upgrade2: 'upgrade2'
    }
    const safePlan = (PLAN_ALIASES[rawPlan] ?? (rawPlan in PLAN_CONFIGS ? (rawPlan as keyof typeof PLAN_CONFIGS) : 'free')) as keyof typeof PLAN_CONFIGS
    const planConfig = PLAN_CONFIGS[safePlan]
    const maxFileSize = planConfig.uploadLimit.amount

    const validFiles = files.filter(file => file.size <= maxFileSize)
    const oversizedFiles = files.filter(file => file.size > maxFileSize)

    if (oversizedFiles.length > 0) {
      toast.error(`${oversizedFiles.length} fil(er) er større end ${formatFileSize(maxFileSize)} og kan ikke uploades.`)
    }

    setSelectedFiles(prev => [...prev, ...validFiles])
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files)
      handleFileSelection(files)
    }
  }

  const removeFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // Helper function for large file uploads (direct to GoFile)
  const handleLargeFileUpload = async (
    file: File, 
    uploadId: string, 
    resolve: (value: { fileId?: string; downloadUrl?: string }) => void,
    reject: (reason: any) => void
  ) => {
    try {
      // Initialize progress tracking
      await fetch('/api/upload-progress/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uploadId })
      })

      // Step 1: Get upload credentials
      const credentialsResponse = await fetch('/api/upload-large', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          filename: file.name,
          size: file.size,
          mimeType: file.type,
          uploadId
        })
      });

      const credentials = await credentialsResponse.json();
      if (!credentials.success) {
        throw new Error(credentials.error || 'Failed to get upload credentials');
      }

      // Step 2: Upload directly to GoFile with progress tracking
      const formData = new FormData();
      formData.append('file', file);
      
      if (credentials.data.token) {
        formData.append('token', credentials.data.token);
      }
      if (credentials.data.folderId) {
        formData.append('folderId', credentials.data.folderId);
      }

      const xhr = new XMLHttpRequest();
      xhr.open('POST', credentials.data.uploadUrl, true);

      // Track upload progress
      xhr.upload.onprogress = async (event) => {
        if (event.lengthComputable) {
          const progress = Math.round((event.loaded / event.total) * 80) + 10; // 10-90% for upload
          await fetch('/api/upload-progress/update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ uploadId, progress, status: 'uploading' })
          });
        }
      };

      xhr.onload = async () => {
        try {
          const text = xhr.responseText;
          let goFileResult: any;
          try {
            goFileResult = JSON.parse(text);
          } catch {
            goFileResult = { success: xhr.status >= 200 && xhr.status < 300, data: { raw: text } };
          }

          if (!goFileResult || goFileResult.status !== 'ok') {
            await fetch('/api/upload-progress/update', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: 'GoFile upload failed' })
            });
            reject(new Error('GoFile upload failed'));
            return;
          }

          // Update progress to processing
          await fetch('/api/upload-progress/update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ uploadId, progress: 90, status: 'processing' })
          });

          // Step 3: Complete the upload by saving to our database
          const completeResponse = await fetch('/api/upload-complete', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              uploadId,
              filename: file.name,
              size: file.size,
              mimeType: file.type,
              goFileResult,
              uploadServer: credentials.data.uploadUrl.split('/')[2] // Extract server from URL
            })
          });

          const completeResult = await completeResponse.json();
          if (completeResult.success) {
            // Update progress to completed
            await fetch('/api/upload-progress/update', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ uploadId, progress: 100, status: 'completed' })
            });
            resolve({
              fileId: completeResult.data?.fileId,
              downloadUrl: completeResult.data?.downloadUrl,
            });
          } else {
            await fetch('/api/upload-progress/update', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: completeResult.error })
            });
            reject(new Error(completeResult.error || 'Failed to complete upload'));
          }
        } catch (err) {
          await fetch('/api/upload-progress/update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: err instanceof Error ? err.message : 'Upload failed' })
          });
          reject(err);
        }
      };

      xhr.onerror = async () => {
        await fetch('/api/upload-progress/update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: 'Network error during upload' })
        });
        reject(new Error("Network error during upload"));
      };

      xhr.onabort = async () => {
        await fetch('/api/upload-progress/update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: 'Upload aborted' })
        });
        reject(new Error("Upload aborted"));
      };

      xhr.send(formData);
    } catch (error) {
      await fetch('/api/upload-progress/update', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: error instanceof Error ? error.message : 'Upload failed' })
      });
      reject(error);
    }
  };

  // Helper to upload a single file with XMLHttpRequest to get real progress events
  const uploadSingleFile = (file: File) =>
    new Promise<{ fileId?: string; downloadUrl?: string }>((resolve, reject) => {
      const formData = new FormData()
      formData.append('file', file, file.name)
      
      // Generate unique upload ID for progress tracking
      const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      formData.append('uploadId', uploadId)

      // Route selection: Use hybrid approach based on file size
      const BYTES_IN_MB = 1024 * 1024
      const STANDARD_CAP_MB = 4 // 4MB limit for Vercel API routes
      const STANDARD_CAP_BYTES = STANDARD_CAP_MB * BYTES_IN_MB
      
      const isLargeFile = file.size > STANDARD_CAP_BYTES
      const endpoint = isLargeFile ? '/api/upload-large' : '/api/upload-with-progress'

      // Initialize progress tracking for both small and large files
      fetch('/api/upload-progress/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ uploadId })
      }).catch(() => {});

      if (isLargeFile) {
        // Large file: use 3-step direct upload process
        handleLargeFileUpload(file, uploadId, resolve, reject)
      } else {
        // Small file: use standard upload with progress
        const xhr = new XMLHttpRequest()
        xhr.open('POST', endpoint)
        xhr.responseType = 'text'
        xhr.setRequestHeader('Accept', 'application/json')

        // Track upload progress for small files
        xhr.upload.onprogress = async (event) => {
          if (event.lengthComputable) {
            const progress = Math.round((event.loaded / event.total) * 80) + 10; // 10-90% for upload
            await fetch('/api/upload-progress/update', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ uploadId, progress, status: 'uploading' })
            }).catch(() => {});
          }
        };

        xhr.onload = async () => {
          

          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const json = JSON.parse(xhr.responseText)
              // Update progress to completed
              await fetch('/api/upload-progress/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ uploadId, progress: 100, status: 'completed' })
              }).catch(() => {});
              resolve({
                fileId: json?.data?.fileId,
                downloadUrl: json?.data?.downloadUrl,
              })
            } catch (e) {
              await fetch('/api/upload-progress/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: 'Failed to parse response' })
              }).catch(() => {});
              resolve({})
            }
          } else {
            try {
              const err = JSON.parse(xhr.responseText)
              await fetch('/api/upload-progress/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: err?.error || 'Upload failed' })
              }).catch(() => {});
              reject(new Error(err?.error || 'Upload failed'))
            } catch {
              await fetch('/api/upload-progress/update', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: 'Upload failed' })
              }).catch(() => {});
              reject(new Error('Upload failed'))
            }
          }
        }

        xhr.onerror = async () => {
          await fetch('/api/upload-progress/update', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ uploadId, progress: 0, status: 'error', error: 'Network error during upload' })
          }).catch(() => {});
          reject(new Error('Network error during upload'))
        }

        // Start the upload immediately
        xhr.send(formData)
      }

      // Set up SSE progress tracking for ALL files (both small and large)
      
      const eventSource = new EventSource(`/api/upload-progress?uploadId=${uploadId}`)
      
      // Fallback polling mechanism in case SSE fails
      let pollInterval: NodeJS.Timeout | null = null
      let lastProgress = -1
      
      const startPolling = () => {
        if (pollInterval) return // Already polling
        
        pollInterval = setInterval(async () => {
          try {
            const response = await fetch(`/api/upload-progress?uploadId=${uploadId}`)
            if (response.ok) {
              const data = await response.json()
              if (data.progress && data.progress.progress !== lastProgress) {
                lastProgress = data.progress.progress
                
                
                if (data.progress.status === 'uploading' || data.progress.status === 'processing') {
                  setUploadProgress(data.progress.progress)
                } else if (data.progress.status === 'completed') {
                  setUploadProgress(100)
                  if (pollInterval) {
                    clearInterval(pollInterval)
                    pollInterval = null
                  }
                } else if (data.progress.status === 'error') {
                  if (pollInterval) {
                    clearInterval(pollInterval)
                    pollInterval = null
                  }
                  reject(new Error(data.progress.error || 'Upload failed'))
                }
              }
            }
          } catch (e) {
          }
        }, 1000) // Poll every second
      }
      
      eventSource.onopen = () => {
        
      }
      
      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          
          
          if (data.status === 'uploading' || data.status === 'processing') {
            setUploadProgress(data.progress)
          } else if (data.status === 'completed') {
            setUploadProgress(100)
            eventSource.close()
            if (pollInterval) {
              clearInterval(pollInterval)
              pollInterval = null
            }
          } else if (data.status === 'error') {
            eventSource.close()
            if (pollInterval) {
              clearInterval(pollInterval)
              pollInterval = null
            }
            reject(new Error(data.error || 'Upload failed'))
          }
        } catch (e) {
          console.error('Failed to parse SSE data:', e)
        }
      }

      eventSource.onerror = (error) => {
        console.error('SSE error:', error)
        eventSource.close()
        // Start polling as fallback when SSE fails
        startPolling()
      }

      // Add timeout to close SSE connection if it takes too long
      setTimeout(() => {
        if (eventSource.readyState !== EventSource.CLOSED) {
          
          eventSource.close()
          startPolling() // Start polling as fallback
        }
      }, 300000) // 5 minutes timeout
    })

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return

    setIsUploading(true)
    setUploadProgress(0)
    setCurrentUploadIndex(0)
    setTotalFiles(selectedFiles.length)
    setUploadStatus('Initialiserer...')

    try {
      const uploadedFiles: Array<{ name: string; fileId?: string; downloadUrl?: string }> = []

      // Upload files sequentially with individual progress tracking
      for (let i = 0; i < selectedFiles.length; i++) {
        const file = selectedFiles[i]
        setCurrentUploadIndex(i + 1)
        setUploadStatus(`Uploader ${file.name}...`)
        
        
        const result = await uploadSingleFile(file)
        uploadedFiles.push({
          name: file.name,
          fileId: result.fileId,
          downloadUrl: result.downloadUrl,
        })
        
        // Small delay between uploads to ensure clean state
        if (i < selectedFiles.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }

      // Ensure progress shows 100% at the end
      setUploadProgress(100)
      setUploadStatus('Færdig!')
      setIsUploading(false)
      setSelectedFiles([])
      setCurrentUploadIndex(0)
      setTotalFiles(0)

      // Reload dashboard data
      await loadDashboardData()

      // Success message: include file names if single, otherwise count
      if (uploadedFiles.length === 1) {
        const only = uploadedFiles[0]?.name || "Fil"
        toast.success(`${only} uploadet succesfuldt!`)
      } else {
        toast.success(`${uploadedFiles.length} filer uploadet succesfuldt!`)
      }
    } catch (error) {
      console.error('Upload error:', error)
      setIsUploading(false)
      setCurrentUploadIndex(0)
      setTotalFiles(0)
      setUploadStatus('')
      toast.error(`Upload fejlede: ${error instanceof Error ? error.message : 'Ukendt fejl'}`)
    }
  }

  const copyDownloadLink = async (file: FileRecord) => {
    try {
      // Build absolute URL to public download page
      const base = typeof window !== 'undefined'
        ? window.location.origin
        : ''
      const downloadUrl = `${base}/d/${file._id}`
      await navigator.clipboard.writeText(downloadUrl)
      toast.success('Download link kopieret til udklipsholder!')
    } catch (error) {
      console.error('Error copying link:', error)
      toast.error('Kunne ikke kopiere download link')
    }
  }

  // Delete both locally and on GoFile (if file has goFileCode)
  // NOTE: Native confirm prompt removed. UI should open DeleteConfirmationModal (see FileActionsDropdown)
  const deleteFile = async (file: FileRecord) => {
    try {
      // 1) Delete from GoFile first if we have a code
      // The code may be stored as file.goFileCode or inside file.external?.gofile?.code depending on your schema.
      const goFileCode =
        (file as any).goFileCode ||
        (file as any)?.external?.gofile?.code ||
        (file as any)?.gofileCode

      if (goFileCode) {
        const gfResp = await fetch(
          `/api/gofile/delete/${encodeURIComponent(goFileCode)}`,
          { method: "DELETE" }
        )
        if (!gfResp.ok) {
          // Try to read error for diagnostics, but don't block local deletion if GoFile deletion fails
          try {
            const err = await gfResp.json()
            console.error("GoFile delete failed:", err)
          } catch {}
        }
      }

      // 2) Soft-delete in our database
      const response = await fetch(`/api/files/${file._id}`, { method: "DELETE" })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Delete failed")
      }

      // 3) Update UI
      await loadDashboardData()
      toast.success("Fil slettet succesfuldt!")
    } catch (error) {
      console.error("Delete error:", error)
      toast.error(
        `Sletning fejlede: ${
          error instanceof Error ? error.message : "Ukendt fejl"
        }`
      )
    }
  }

  // Download limit editing functions
  const startEditingLimit = (fileId: string, currentLimit: number) => {
    setEditingLimit(fileId)
    // Convert -1 (unlimited) to 0 for the input field, since 0 means unlimited
    setEditingValue(currentLimit === -1 ? '0' : currentLimit.toString())
  }

  const saveDownloadLimit = async (fileId: string) => {
    try {
      setSavingLimit(fileId)
      
      // Parse the new limit value
      let newLimit: number
      if (editingValue.trim() === '') {
        newLimit = -1 // Unlimited
      } else {
        const parsed = parseInt(editingValue.trim())
        if (isNaN(parsed) || parsed < 0) {
          toast.error('Indtast venligst et gyldigt tal (0 eller højere)')
          return
        }
        // 0 means unlimited, so convert to -1
        newLimit = parsed === 0 ? -1 : parsed
      }

      // Update the file's download limit
      const response = await fetch(`/api/files/${fileId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          downloadLimit: newLimit
        })
      })

      if (!response.ok) {
        throw new Error('Kunne ikke opdatere download grænse')
      }

      // Update local state
      setFiles(prev => prev.map(file => 
        file._id === fileId 
          ? { ...file, downloadLimit: newLimit }
          : file
      ))

      toast.success('Download grænse opdateret')
      setEditingLimit(null)
      setEditingValue('')
    } catch (error) {
      toast.error(`Fejl ved opdatering: ${error instanceof Error ? error.message : 'Ukendt fejl'}`)
    } finally {
      setSavingLimit(null)
    }
  }

  const cancelEditingLimit = () => {
    setEditingLimit(null)
    setEditingValue('')
  }



  if (status === "loading" || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Indlæser dashboard...</p>
        </div>
      </div>
    )
  }
 
  const guestMode = !session
 
  // Normalize user plan (handle aliases like 'basis'/'pro') - single source of truth
  const rawPlan = ((guestMode ? 'guest' : ((session.user as any)?.plan as string) || 'free')).toLowerCase()
  const PLAN_ALIASES: Record<string, keyof typeof PLAN_CONFIGS> = {
    guest: 'guest',
    free: 'free',
    gratis: 'free',
    basis: 'upgrade1',
    basic: 'upgrade1',
    upgrade1: 'upgrade1',
    pro: 'upgrade2',
    upgrade2: 'upgrade2'
  }
  const safePlan = (PLAN_ALIASES[rawPlan] ?? (rawPlan in PLAN_CONFIGS ? (rawPlan as keyof typeof PLAN_CONFIGS) : 'free')) as keyof typeof PLAN_CONFIGS
  const planConfig = PLAN_CONFIGS[safePlan]
  const usagePercentage = usage && planConfig ? calculateUsagePercentage(usage.current, planConfig.uploadLimit.amount) : 0

  // Compute next reset time based on plan period
  const getNextResetDate = (period: string): Date | null => {
    const now = new Date()
    if (period === 'uge') {
      // Next Monday at 00:00 (DA locale uses Monday as first weekday)
      const day = now.getDay() // 0=Sun, 1=Mon, ..., 6=Sat
      const daysUntilMonday = ((1 - day + 7) % 7) || 7
      const next = new Date(now)
      next.setDate(now.getDate() + daysUntilMonday)
      next.setHours(0, 0, 0, 0)
      return next
    }
    if (period === 'måned') {
      // First day of next month at 00:00
      const next = new Date(now.getFullYear(), now.getMonth() + 1, 1)
      next.setHours(0, 0, 0, 0)
      return next
    }
    // Session-based
    return null
  }

  const nextResetAt = getNextResetDate(planConfig.uploadLimit.period)

  return (
    <main className="min-h-screen bg-gradient-to-b from-gray-50 to-gray-100">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-blue-700 to-blue-900 text-white py-16 sm:py-20 overflow-hidden">
        <div className="container mx-auto px-4 sm:px-6 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center justify-center mb-4 sm:mb-6 px-4 py-2 bg-blue-600/30 rounded-full backdrop-blur-sm text-blue-100 text-sm font-medium cursor-default">
              Velkommen tilbage
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-extrabold mb-4 sm:mb-6 leading-tight">
              Hej <span className="text-blue-300">{guestMode ? 'Gæst' : (session.user?.name)}</span>
            </h1>
            <p className="text-lg sm:text-xl text-blue-100 mb-6 sm:mb-8 max-w-2xl mx-auto">
              Administrer dine filer og se din forbrugsstatistik
            </p>
          </div>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">

        {/* Usage Overview */}
        {usage && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Upload forbrug</span>
                </CardTitle>
                <CardDescription>
                  Din nuværende {usage.period} forbrug
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-2xl font-bold">
                      {formatFileSize(usage.current)} / {formatFileSize(usage.limit)}
                    </span>
                    <span className="text-sm text-gray-600 dark:text-gray-400">
                      {usage.percentage.toFixed(1)}% brugt
                    </span>
                  </div>
                  <Progress value={usage.percentage} className="h-3" />
                  {/* Next reset notice */}
                  <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                    {planConfig.uploadLimit.period === 'session' && (
                      <span>Nulstilles ved logud</span>
                    )}
                    {planConfig.uploadLimit.period !== 'session' && nextResetAt && (
                      <span>
                        Nulstilles {planConfig.uploadLimit.period === 'uge' ? 'mandag kl. 00:00' : 'd. 1. kl. 00:00'}
                        {' '}(<span className="whitespace-nowrap">{nextResetAt.toLocaleString('da-DK')}</span>)
                      </span>
                    )}
                  </div>
                  <div className="flex items-center justify-between">
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Plan: {getPlanDisplayName(usage.plan)}
                    </p>
                    {!guestMode && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsRequestOpen(true)}
                        className="ml-4"
                      >
                        Anmod om mere
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Plan status</CardTitle>
                <CardDescription>
                  Din nuværende plan og fordele
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="font-semibold">{planConfig.name}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    • {formatFileSize(planConfig.uploadLimit.amount)} per {planConfig.uploadLimit.period === 'uge' ? 'uge' : planConfig.uploadLimit.period === 'måned' ? 'måned' : 'session'}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    • {planConfig.fileExpiry} dages opbevaring
                  </div>
                  {'price' in planConfig && (
                    <div className="text-sm text-gray-600 dark:text-gray-400">
                      • {planConfig.price.amount} kr/måned
                    </div>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-4 w-full"
                    onClick={() => router.push('/pricing')}
                  >
                    Skift plan
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Upload Area */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Upload className="h-5 w-5" />
              <span>Upload nye filer</span>
            </CardTitle>
            <CardDescription>
              Drag og slip filer her eller klik for at vælge filer
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div
              className={`border-2 border-dashed transition-all duration-300 rounded-lg p-8 text-center cursor-pointer ${
                isDragOver
                  ? 'border-blue-500 bg-blue-100/50 scale-105'
                  : 'border-gray-300 hover:border-blue-500 dark:border-gray-700 dark:hover:border-blue-500'
              }`}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={() => { fileInputRef.current?.click() }}
            >
              <div className={`w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4 transition-all duration-300 ${
                isDragOver ? 'scale-110 bg-blue-700' : ''
              }`}>
                <Upload className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-800 mb-2">
                {isDragOver ? 'Slip filerne her!' : 'Drag filer hertil eller klik for at uploade'}
              </h3>
              <p className="text-gray-600 mb-2">
                Maksimal filstørrelse: {formatFileSize(planConfig.uploadLimit.amount)}
              </p>
              <p className="text-xs text-gray-500">
                Kun filer er tilladt. Mapper kan ikke uploades via drag-and-drop.
              </p>

              <input
                ref={fileInputRef}
                type="file"
                multiple
                className="hidden"
                onChange={handleFileInputChange}
                accept="*/*"
                
              />

              <Button disabled={isUploading}>
                {isUploading ? 'Uploader...' : 'Vælg filer'}
              </Button>
            </div>

            {/* Selected Files Display */}
            {selectedFiles.length > 0 && (
              <div className="mt-8 border-t pt-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-semibold text-gray-800">
                    Valgte filer ({selectedFiles.length})
                  </h4>
                  
                  {/* Upload Button - Now positioned at the top right for easy access */}
                  {!isUploading && (
                    <Button
                      onClick={handleUpload}
                      className="bg-green-600 hover:bg-green-700 px-6"
                      size="lg"
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload {selectedFiles.length} fil{selectedFiles.length !== 1 ? 'er' : ''}
                    </Button>
                  )}
                </div>

                {/* Files List */}
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {selectedFiles.map((file, index) => {
                    // Derive a stable current index: while uploading, currentUploadIndex points to "1-based" progress state
                    // We show current item as (currentUploadIndex - 1)
                    const activeIndex = isUploading ? Math.max(0, currentUploadIndex - 1) : -1
                    const isCurrent = isUploading && index === activeIndex
                    const isQueued = isUploading && index > activeIndex
                    // Mark completed strictly for indices less than activeIndex while uploading
                    // and after uploads finished, all become completed
                    const isCompleted =
                      (!isUploading && currentUploadIndex > 0) ||
                      (isUploading && index < activeIndex) ||
                      (!isUploading && index < currentUploadIndex)
                    return (
                      <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                        <div className="flex items-center space-x-3 min-w-0">
                          <File className="h-5 w-5 text-blue-600 shrink-0" />
                          <div className="min-w-0">
                            <div className="flex items-center gap-2">
                              <p className="font-medium text-gray-800 truncate max-w-xs">{file.name}</p>
                              {/* Status badge */}
                              {isCurrent && (
                                <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-blue-100 text-blue-800 border border-blue-200">
                                  <svg className="animate-spin -ml-0.5 mr-1 h-3 w-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" aria-hidden="true">
                                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="3"></circle>
                                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v3A5 5 0 007 12H4z"></path>
                                  </svg>
                                  Uploader
                                </span>
                              )}
                              {isQueued && (
                                <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-gray-100 text-gray-700 border border-gray-200">
                                  <svg className="h-3.5 w-3.5 mr-1 text-gray-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" aria-hidden="true">
                                    <path d="M12 2a10 10 0 1010 10A10.011 10.011 0 0012 2zm.75 5.25a.75.75 0 00-1.5 0V12a.75.75 0 00.22.53l3 3a.75.75 0 001.06-1.06l-2.78-2.78z" />
                                  </svg>
                                  Queued
                                </span>
                              )}
                              {isCompleted ? (
                                <span className="inline-flex items-center px-2 py-0.5 text-xs font-medium rounded bg-green-100 text-green-800 border border-green-200">
                                  Færdig
                                </span>
                              ) : null}
                            </div>
                            {/* Per-file progress indicator for current file */}
                            {isCurrent && (
                              <div className="mt-2">
                                <div className="w-48 h-2 bg-gray-200 rounded">
                                  <div
                                    className="h-2 rounded bg-blue-600 transition-all"
                                    style={{ width: `${uploadProgress}%` }}
                                  />
                                </div>
                                <div className="mt-1 text-[11px] text-gray-600">{uploadProgress}%</div>
                              </div>
                            )}
                          </div>
                        </div>
                        {!isUploading && (
                          <button
                            onClick={() => removeFile(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <X className="h-5 w-5" />
                          </button>
                        )}
                      </div>
                    )
                  })}
                </div>

                {/* Upload Progress */}
                {isUploading && (
                  <div className="mt-4">
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                      <span>
                        {totalFiles > 1 ? `Fil ${currentUploadIndex} af ${totalFiles}: ` : ''}
                        {uploadStatus}
                      </span>
                      <span>{uploadProgress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      {uploadProgress < 10 && 'Initialiserer...'}
                      {uploadProgress >= 10 && uploadProgress < 90 && 'Uploader til Serverne...'}
                      {uploadProgress >= 90 && uploadProgress < 100 && 'Behandler fil...'}
                      {uploadProgress === 100 && 'Færdig!'}
                    </div>
                  </div>
                )}

                {/* Helpful tips and info */}
                {!isUploading && (
                  <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-start space-x-3">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="text-sm text-blue-800">
                        <p className="font-medium mb-1">Upload tips:</p>
                        <ul className="space-y-1 text-xs">
                          <li>• Har du brug for at forlænge en fils levetid? Vælg bevaringsperiode i "Indstillinger"</li>
                          <li>• Hvis filen er sensitiv, kan vi slette den med det samme via en support-ticket (svar inden for 12 timer)</li>
                          <li>• Du kan tilføje flere filer ved at klikke på upload-området igen</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Folder blocked modal */}
        {folderModalOpen && (
          <div
            className="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
            role="dialog"
            aria-modal="true"
          >
            <Card className="w-full max-w-md">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-lg font-bold">
                  Mapper er ikke tilgængelige
                </CardTitle>
                <button
                  onClick={() => setFolderModalOpen(false)}
                  className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                  aria-label="Luk"
                >
                  <X className="h-5 w-5" />
                </button>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                  Mappe-upload er ikke tilgængelig. Vælg venligst individuelle filer.
                </p>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    onClick={() => setFolderModalOpen(false)}
                  >
                    Luk
                  </Button>
                  <Button 
                    onClick={() => { 
                      setFolderModalOpen(false); 
                      fileInputRef.current?.click() 
                    }}
                  >
                    Vælg filer
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Files Table */}
        <Card className="overflow-hidden">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
            <CardTitle className="flex items-center space-x-2">
              <File className="h-5 w-5 text-blue-600" />
              <span>Mine filer</span>
            </CardTitle>
            <CardDescription>
              Administrer dine uploadede filer og download-grænser
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {files.length === 0 ? (
              <div className="text-center py-12 px-4">
                <div className="mx-auto w-24 h-24 rounded-full bg-blue-50 flex items-center justify-center mb-6">
                  <File className="h-12 w-12 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">Ingen filer endnu</h3>
                <p className="text-gray-600 max-w-md mx-auto mb-6">
                  Upload din første fil for at se den her. Dine filer vil blive vist i en overskuelig liste med alle nødvendige oplysninger.
                </p>
                <Button onClick={() => fileInputRef.current?.click()} className="mb-8">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload fil
                </Button>

                {/* Empty-state retention notice */}
                <div className="mx-auto max-w-2xl rounded-xl border border-yellow-200 bg-yellow-50 p-5 text-left">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-yellow-800">Vigtig information om filopbevaring</h3>
                      <div className="mt-2 text-sm text-yellow-700">
                        <p>
                          Standard opbevaringsperiode er {planConfig.fileExpiry} dage med automatisk sletning. Har du behov for at få slettet en fil med det samme for at sikre, at ingen kan få adgang, kan du oprette en support-ticket. Vi svarer inden for 12 timer.
                        </p>
                      </div>
                      <div className="mt-4 flex space-x-3">
                        <button
                          onClick={() => router.push('/support?subject=slet-filer')}
                          className="inline-flex items-center rounded-md bg-yellow-800 px-3 py-2 text-sm font-medium text-white shadow-sm hover:bg-yellow-900 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2"
                        >
                          Anmod om sletning
                        </button>
                        <a
                          href="/support"
                          className="inline-flex items-center rounded-md border border-yellow-300 bg-white px-3 py-2 text-sm font-medium text-yellow-800 hover:bg-yellow-50"
                        >
                          Læs mere
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="overflow-x-auto">
                {/* Info banner */}
                <div className="bg-blue-50 border-b border-blue-100 p-4">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                    <p className="text-sm text-blue-800">
                      <span className="font-medium">Standard opbevaringsperiode:</span> {planConfig.fileExpiry} dage. Filer slettes automatisk herefter.
                    </p>
                    <button
                      onClick={() => router.push('/support?subject=slet-filer')}
                      className="inline-flex items-center rounded-md bg-blue-600 px-3 py-1.5 text-xs font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      Anmod om sletning
                    </button>
                  </div>
                </div>
                
                {/* Responsive file grid for larger screens, table for smaller */}
                <div className="hidden md:block">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Fil</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Størrelse</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Upload dato</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Udløber</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Downloads</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">
                          Grænse
                          {!guestMode && (
                            <div className="flex items-center gap-1 mt-1">
                              <span className="text-xs text-blue-600">(klik for at redigere)</span>
                            </div>
                          )}
                        </th>
                        <th className="text-right py-3 px-4 font-medium text-gray-500 uppercase tracking-wider">Handlinger</th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-100">
                      {files.map((file) => {
                        // Check if file expires soon (within 2 days)
                        const expiryDate = new Date(file.expiryDate);
                        const today = new Date();
                        const timeDiff = expiryDate.getTime() - today.getTime();
                        const daysUntilExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));
                        const expiresSoon = daysUntilExpiry <= 2 && daysUntilExpiry >= 0;
                        
                        return (
                          <tr key={file._id} className={`hover:bg-gray-50 ${expiresSoon ? 'bg-red-50' : ''}`}>
                            <td className="py-4 px-4">
                              <div className="flex items-center">
                                <div className="flex-shrink-0 h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                                  <File className="h-5 w-5 text-blue-600" />
                                </div>
                                <div className="ml-4">
                                  <div className="font-medium text-gray-900">{file.originalName}</div>
                                  <div className="text-sm text-gray-500">{file.mimeType}</div>
                                </div>
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="text-sm text-gray-900">{formatFileSize(file.size)}</div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="text-sm text-gray-900">{formatDate(file.uploadDate)}</div>
                            </td>
                            <td className="py-4 px-4">
                              <div className={`text-sm font-medium ${expiresSoon ? 'text-red-600' : 'text-gray-900'}`}>
                                {formatDate(file.expiryDate)}
                                {expiresSoon && (
                                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                    Udløber snart
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="py-4 px-4">
                              <div className="text-sm text-gray-900">{file.downloadCount}</div>
                            </td>
                            <td className="py-4 px-4">
                              {guestMode ? (
                                <div className="flex items-center gap-2">
                                  <span className="text-gray-800 dark:text-gray-200 font-medium">Ubegrænset</span>
                                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">Gæst</span>
                                </div>
                              ) : (
                                <div className="flex items-center gap-2">
                                  {editingLimit === file._id ? (
                                    <div className="flex items-center gap-2">
                                      <div className="flex flex-col">
                                        <input
                                          type="number"
                                          value={editingValue}
                                          onChange={(e) => setEditingValue(e.target.value)}
                                          onBlur={() => saveDownloadLimit(file._id)}
                                          onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                              saveDownloadLimit(file._id)
                                            } else if (e.key === 'Escape') {
                                              cancelEditingLimit()
                                            }
                                          }}
                                          placeholder="0 = ubegrænset"
                                          min="0"
                                          className="w-24 text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          autoFocus
                                        />
                                        <div className="flex items-center gap-1 mt-1">
                                          <button
                                            type="button"
                                            onClick={() => setEditingValue('0')}
                                            className="text-xs text-blue-600 hover:text-blue-800 underline hover:bg-blue-50 px-1 py-0.5 rounded transition-colors"
                                            title="Sæt input til 0 (ubegrænset) - klik Gem for at gemme"
                                          >
                                            Sæt ubegrænset
                                          </button>
                                        </div>
                                      </div>
                                      <button
                                        onClick={() => saveDownloadLimit(file._id)}
                                        disabled={savingLimit === file._id}
                                        className="p-1 text-green-600 hover:text-green-700 hover:bg-green-50 rounded transition-colors"
                                        title="Gem"
                                      >
                                        <Check className="h-4 w-4" />
                                      </button>
                                      <button
                                        onClick={cancelEditingLimit}
                                        disabled={savingLimit === file._id}
                                        className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors"
                                        title="Annuller"
                                      >
                                        <X className="h-4 w-4" />
                                      </button>
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-2">
                                      <button
                                        onClick={() => startEditingLimit(file._id, file.downloadLimit)}
                                        className="flex items-center gap-2 text-gray-800 dark:text-gray-200 hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded-md transition-all cursor-pointer group border border-transparent hover:border-blue-200"
                                        title="Klik for at redigere download grænse"
                                      >
                                        <span className="font-medium">
                                          {file.downloadLimit === -1 ? 'Ubegrænset' : file.downloadLimit}
                                        </span>
                                        {file.downloadLimit === -1 ? (
                                          <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">∞</span>
                                        ) : (
                                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">Limit</span>
                                        )}
                                        <Edit className="h-4 w-4 text-blue-500 group-hover:scale-110 transition-transform" />
                                      </button>
                                    </div>
                                  )}
                                  {savingLimit === file._id && (
                                    <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                  )}
                                </div>
                              )}
                            </td>
                            <td className="py-4 px-4 text-right">
                              <FileActionsDropdown
                                file={file}
                                open={openDropdown === file._id}
                                onOpenChange={(open) => setOpenDropdown(open ? file._id : null)}
                                onCopyLink={copyDownloadLink}
                                onDelete={deleteFile}
                                onEditLimit={() => startEditingLimit(file._id, file.downloadLimit)}
                                onSaveLimit={saveDownloadLimit}
                                onCancelLimit={cancelEditingLimit}
                                isEditing={editingLimit === file._id}
                                editingValue={editingValue}
                                setEditingValue={setEditingValue}
                                savingLimit={savingLimit === file._id}
                              />
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
                
                {/* Mobile view - card layout */}
                <div className="md:hidden">
                  <div className="divide-y divide-gray-100">
                    {files.map((file) => {
                      // Check if file expires soon (within 2 days)
                      const expiryDate = new Date(file.expiryDate);
                      const today = new Date();
                      const timeDiff = expiryDate.getTime() - today.getTime();
                      const daysUntilExpiry = Math.ceil(timeDiff / (1000 * 3600 * 24));
                      const expiresSoon = daysUntilExpiry <= 2 && daysUntilExpiry >= 0;
                      
                      return (
                        <div key={file._id} className={`p-4 ${expiresSoon ? 'bg-red-50' : ''}`}>
                          <div className="flex items-start">
                            <div className="flex-shrink-0 h-12 w-12 rounded-lg bg-blue-100 flex items-center justify-center">
                              <File className="h-6 w-6 text-blue-600" />
                            </div>
                            <div className="ml-4 flex-1">
                              <div className="flex items-center justify-between">
                                <h3 className="font-medium text-gray-900">{file.originalName}</h3>
                                <FileActionsDropdown
                                  file={file}
                                  open={openDropdown === file._id}
                                  onOpenChange={(open) => setOpenDropdown(open ? file._id : null)}
                                  onCopyLink={copyDownloadLink}
                                  onDelete={deleteFile}
                                  onEditLimit={() => startEditingLimit(file._id, file.downloadLimit)}
                                  onSaveLimit={saveDownloadLimit}
                                  onCancelLimit={cancelEditingLimit}
                                  isEditing={editingLimit === file._id}
                                  editingValue={editingValue}
                                  setEditingValue={setEditingValue}
                                  savingLimit={savingLimit === file._id}
                                />
                              </div>
                              <p className="text-sm text-gray-500 mt-1">{file.mimeType}</p>
                              
                              <div className="mt-3 grid grid-cols-2 gap-2">
                                <div>
                                  <p className="text-xs text-gray-500">Størrelse</p>
                                  <p className="text-sm font-medium">{formatFileSize(file.size)}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-gray-500">Downloads</p>
                                  <p className="text-sm font-medium">{file.downloadCount}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-gray-500">Upload dato</p>
                                  <p className="text-sm font-medium">{formatDate(file.uploadDate)}</p>
                                </div>
                                <div>
                                  <p className="text-xs text-gray-500">Udløber</p>
                                  <div className={`text-sm font-medium ${expiresSoon ? 'text-red-600' : 'text-gray-900'}`}>
                                    {formatDate(file.expiryDate)}
                                    {expiresSoon && (
                                      <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Udløber snart
                                      </span>
                                    )}
                                  </div>
                                </div>
                                <div className="col-span-2">
                                  <p className="text-xs text-gray-500">Download grænse</p>
                                  {guestMode ? (
                                    <div className="flex items-center gap-2">
                                      <span className="text-gray-800 dark:text-gray-200 font-medium text-sm">Ubegrænset</span>
                                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-0.5 rounded-full">Gæst</span>
                                    </div>
                                  ) : (
                                    <div className="flex items-center gap-2">
                                      {editingLimit === file._id ? (
                                        <div className="flex items-center gap-2">
                                          <input
                                            type="number"
                                            value={editingValue}
                                            onChange={(e) => setEditingValue(e.target.value)}
                                            onBlur={() => saveDownloadLimit(file._id)}
                                            onKeyDown={(e) => {
                                              if (e.key === 'Enter') {
                                                saveDownloadLimit(file._id)
                                              } else if (e.key === 'Escape') {
                                                cancelEditingLimit()
                                              }
                                            }}
                                            placeholder="0 = ubegrænset"
                                            min="0"
                                            className="w-24 text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                            autoFocus
                                          />
                                          <button
                                            onClick={() => saveDownloadLimit(file._id)}
                                            disabled={savingLimit === file._id}
                                            className="p-1 text-green-600 hover:text-green-700 hover:bg-green-50 rounded transition-colors"
                                            title="Gem"
                                          >
                                            <Check className="h-4 w-4" />
                                          </button>
                                          <button
                                            onClick={cancelEditingLimit}
                                            disabled={savingLimit === file._id}
                                            className="p-1 text-gray-500 hover:text-gray-700 hover:bg-gray-50 rounded transition-colors"
                                            title="Annuller"
                                          >
                                            <X className="h-4 w-4" />
                                          </button>
                                        </div>
                                      ) : (
                                        <button
                                          onClick={() => startEditingLimit(file._id, file.downloadLimit)}
                                          className="flex items-center gap-2 text-gray-800 dark:text-gray-200 hover:text-blue-600 hover:bg-blue-50 px-2 py-1 rounded-md transition-all cursor-pointer group border border-transparent hover:border-blue-200"
                                          title="Klik for at redigere download grænse"
                                        >
                                          <span className="font-medium text-sm">
                                            {file.downloadLimit === -1 ? 'Ubegrænset' : file.downloadLimit}
                                          </span>
                                          {file.downloadLimit === -1 ? (
                                            <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">∞</span>
                                          ) : (
                                            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">Limit</span>
                                          )}
                                          <Edit className="h-4 w-4 text-blue-500 group-hover:scale-110 transition-transform" />
                                        </button>
                                      )}
                                      {savingLimit === file._id && (
                                        <svg className="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Request More Modal componentized */}
      <RequestMoreUsageModal
        open={isRequestOpen}
        onOpenChange={(o: boolean) => {
          setIsRequestOpen(o)
          if (!o) resetRequestForm()
        }}
        onSubmit={(amount: string, message: string) => {
          setRequestAmount(amount)
          setRequestMessage(message)
          submitRequest()
        }}
        submitting={requestSubmitting}
        amount={requestAmount}
        message={requestMessage}
        setAmount={setRequestAmount}
        setMessage={setRequestMessage}
        onResetForm={resetRequestForm}
      />
    </main>
)
}